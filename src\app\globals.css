@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 208 100% 97%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 197 71% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 120 73% 70%;
    --accent-foreground: 120 25% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 197 71% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 197 71% 73%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 120 73% 75%;
    --accent-foreground: 120 25% 15%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 197 71% 73%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Arabic font support */
  .font-arabic {
    font-family: 'Noto Sans Arabic', 'Inter', system-ui, -apple-system, sans-serif;
  }
}



/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .btn-icon-right {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* RTL specific adjustments - Fix flex direction issues */
[dir="rtl"] .flex:not(.flex-col):not(.flex-row) {
  flex-direction: row-reverse;
}

[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* Form inputs RTL */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="number"],
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
  padding-left: 0.75rem;
  padding-right: 2.5rem;
  direction: rtl;
}

[dir="rtl"] .pl-10 {
  padding-left: 0.75rem;
  padding-right: 2.5rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

/* Icon positioning in RTL */
[dir="rtl"] .absolute.left-3 {
  left: auto;
  right: 0.75rem;
}

[dir="rtl"] .absolute.right-3 {
  right: auto;
  left: 0.75rem;
}

/* Navigation RTL */
[dir="rtl"] .rotate-180 {
  transform: rotate(0deg);
}

[dir="rtl"] .rotate-0 {
  transform: rotate(180deg);
}

/* Grid and layout fixes for RTL */
[dir="rtl"] .grid {
  direction: rtl;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* Header and navigation fixes */
[dir="rtl"] .justify-between {
  direction: rtl;
}

[dir="rtl"] .items-center {
  direction: rtl;
}

/* Arabic numbers styling */
.arabic-numbers {
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
}

/* Enhanced Arabic UI */
[dir="rtl"] .arabic-numbers {
  font-family: "IBM Plex Sans Arabic", sans-serif;
  font-weight: 500;
}

[dir="rtl"] .form-label {
  text-align: right;
  display: block;
  width: 100%;
}

[dir="rtl"] .input-wrapper {
  display: flex;
  flex-direction: row-reverse;
}

[dir="rtl"] .input-icon {
  margin-left: 0;
  margin-right: 8px;
}

/* Fix text alignment in cards for RTL */
[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-center {
  text-align: center !important;
}

/* Fix spacing and margins in RTL */
[dir="rtl"] .space-y-3 > * + * {
  margin-top: 0.75rem;
}

[dir="rtl"] .space-y-4 > * + * {
  margin-top: 1rem;
}

/* Fix button icons in RTL */
[dir="rtl"] .ml-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

[dir="rtl"] .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

/* Fix card content alignment */
[dir="rtl"] .flex.justify-between {
  text-align: right;
}

[dir="rtl"] .flex.justify-between > *:first-child {
  text-align: right;
}

[dir="rtl"] .flex.justify-between > *:last-child {
  text-align: left;
}

/* Fix overlapping text in results */
[dir="rtl"] .flex.justify-between.items-center {
  gap: 1rem;
  min-height: 2rem;
}

[dir="rtl"] .flex-shrink-0 {
  flex-shrink: 0;
  max-width: 60%;
}

[dir="rtl"] .text-right {
  text-align: right !important;
  flex-shrink: 0;
  min-width: 35%;
}

/* Ensure proper spacing in Arabic layout */
[dir="rtl"] .w-full.space-y-3 {
  width: 100%;
}

[dir="rtl"] .w-full.space-y-3 > div {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

/* Fix header text overlap */
[dir="rtl"] .text-4xl,
[dir="rtl"] .text-5xl {
  line-height: 1.2;
  word-spacing: 0.1em;
}

/* Ensure proper text wrapping */
[dir="rtl"] .max-w-xs,
[dir="rtl"] .max-w-sm,
[dir="rtl"] .max-w-md {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Fix layout stability - prevent width changes */
@layer utilities {
  .grid-stable {
    grid-template-columns: 1fr;
  }

  @media (min-width: 768px) {
    .grid-stable {
      grid-template-columns: 1fr 1fr;
    }
  }

  /* Ensure cards maintain consistent width */
  .card-stable {
    flex: 1 1 0%;
    min-width: 0;
    width: 100%;
  }

  /* Prevent layout shift during calculations */
  .results-stable {
    min-height: 550px;
    transition: none;
  }

  /* Stable container width */
  .container-stable {
    max-width: 56rem;
    min-width: 0;
    width: 100%;
  }

  /* Arabic font styling */
  .font-arabic {
    font-family: 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
  }

  /* RTL specific styles */
  [dir="rtl"] {
    text-align: right;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }
}
